import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:flutter_application_2/models/message_models.dart';
// import 'package:flutter_application_2/services/signalr_service.dart'; // معلق مؤقتاً
import 'package:flutter_application_2/services/unified_signalr_service.dart'; // الخدمة الموحدة الجديدة
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../models/task_comment_models.dart';
import '../models/task_history_models.dart';
import '../models/subtask_models.dart';
import '../models/task_progress_models.dart';
import '../models/time_tracking_models.dart';
import '../services/api/task_api_service.dart';
import '../services/api/task_comments_api_service.dart';
import '../services/api/task_history_api_service.dart';
import '../services/api/subtasks_api_service.dart';
import '../services/api/task_access_api_service.dart';
import 'subtasks_controller.dart';
import '../models/task_contributor_model.dart';
import '../services/api/task_progress_trackers_api_service.dart';
import '../services/cache_service.dart';
import '../services/api/attachments_api_service.dart'; // إضافة خدمة المرفقات
import '../services/api/time_tracking_api_service.dart'; // إضافة خدمة تتبع الوقت
import '../utils/user_helper.dart';
import '../utils/error_handler.dart';
import '../utils/api_diagnostics.dart';
import '../models/task_status_enum.dart' as task_enums;
import 'simple_task_comments_controller.dart';
import '../services/unified_permission_service.dart';

/// مستويات الوصول للمستخدمين في النظام الهرمي
enum UserAccessLevel {
  /// مستخدم عادي - يرى مهامه الشخصية فقط
  regularUser,
  /// مدير إدارة - يرى مهام إدارته والأقسام التابعة
  departmentManager,
  /// مدير النظام - يرى جميع المهام
  systemAdmin,
}

/// متحكم المهام المحسن مع التخزين المؤقت ومعالجة الأخطاء
class TaskController extends GetxController {
  final TaskApiService _apiService = TaskApiService();
  final TaskCommentsApiService _commentsApiService = TaskCommentsApiService();
  final TaskHistoryApiService _historyApiService = TaskHistoryApiService();
  final SubtasksApiService _subtasksApiService = SubtasksApiService();
  final TaskProgressTrackersApiService _progressTrackersApiService =
      TaskProgressTrackersApiService();
  final AttachmentsApiService _attachmentsApiService =
      AttachmentsApiService(); // تهيئة خدمة المرفقات
  final TaskAccessApiService _taskAccessApiService = TaskAccessApiService();
  final TimeTrackingApiService _timeTrackingApiService = TimeTrackingApiService(); // تهيئة خدمة تتبع الوقت
  final CacheService _cacheService = CacheService();

  // قوائم المهام
  final RxList<Task> _allTasks = <Task>[].obs;
  final RxList<Task> _filteredTasks = <Task>[].obs;
  final RxList<Task> _myTasks = <Task>[].obs;
  final RxList<Task> _assignedTasks = <Task>[].obs;
  final RxList<TaskComment> _taskComments = <TaskComment>[].obs;
  final RxList<TaskHistory> _taskHistory = <TaskHistory>[].obs;
  final RxList<Subtask> _subtasks = <Subtask>[].obs;
  final RxList<Map<String, dynamic>> _taskMessages =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _userContributions =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _messageAttachments =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _progressTrackers =
      <Map<String, dynamic>>[].obs;
  final RxList<TaskContributor> _taskContributors = <TaskContributor>[].obs;
  final RxString _selectedContributorId = ''.obs;

  // Progress tracking variables - إضافة المتغيرات المطلوبة للتوافق مع TaskProgressTab
  final Rx<TaskProgressSummary?> _progressSummary =
      Rx<TaskProgressSummary?>(null);
  final Rx<TaskTimeTrackingSummary?> _timeTrackingSummary =
      Rx<TaskTimeTrackingSummary?>(null);
  final RxList<TimeEntry> _timeEntries = <TimeEntry>[].obs;
  final RxBool _isTrackingTime = false.obs;
  final RxBool _isLoadingTimeTracking = false.obs;

  // المهمة الحالية
  final Rx<Task?> _currentTask = Rx<Task?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isSendingMessage = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _statusFilter = Rx<String?>(null);
  final Rx<String?> _priorityFilter = Rx<String?>(null);
  final Rx<int?> _assigneeFilter = Rx<int?>(null);
  final Rx<int?> _departmentFilter = Rx<int?>(null);
  final RxBool _showOverdueOnly = false.obs;

  // Getters
  List<Task> get allTasks => _allTasks;
  List<Task> get filteredTasks => _filteredTasks;
  List<Task> get myTasks => _myTasks;
  List<Task> get assignedTasks => _assignedTasks;
  List<Task> get tasks => _allTasks; // Alias for compatibility
  List<TaskComment> get taskComments => _taskComments;
  RxList<TaskHistory> get taskHistory => _taskHistory;
  RxList<Subtask> get subtasks => _subtasks;
  RxList<TaskComment> get comments =>
      _taskComments; // تم التصحيح من _comments إلى _taskComments
  RxList<Map<String, dynamic>> get taskMessages => _taskMessages;
  RxList<Map<String, dynamic>> get userContributions => _userContributions;
  RxList<Map<String, dynamic>> get messageAttachments => _messageAttachments;
  RxList<Map<String, dynamic>> get progressTrackers => _progressTrackers;
  RxList<TaskContributor> get taskContributors => _taskContributors;
  
  // Selected contributor for contributions view
  String get selectedContributorId => _selectedContributorId.value;
  set selectedContributorId(String value) => _selectedContributorId.value = value;
  
  // Get contributor by user ID
  TaskContributor? getContributorByUserId(int userId) {
    try {
      return _taskContributors.firstWhere((contributor) => contributor.userId == userId);
    } catch (e) {
      return null;
    }
  }
  Task? get currentTask => _currentTask.value;
  bool get isLoading => _isLoading.value;
  bool get isSendingMessage => _isSendingMessage.value;
  String get error => _error.value;
  String get errorMessage =>
      _error.value; // تم التصحيح من _errorMessage إلى _error

  /// تحديث المهمة الحالية
  void updateCurrentTask(Task? task) {
    _currentTask.value = task;
  }

  /// إعادة تحميل تفاصيل المهمة بالكامل مع جميع البيانات المرتبطة
  Future<void> refreshTaskDetails(int taskId) async {
    try {
      _isLoading.value = true;
      
      // إعادة تحميل المهمة من الخادم
      await loadTaskById(taskId, forceRefresh: true);
      
      // إعادة تحميل التعليقات
      await loadTaskComments(taskId);
      
      // إعادة تحميل المساهمين
      await loadTaskContributors(taskId);
      
      // إعادة تحميل سجل التقدم
      await loadTaskProgressSummary(taskId);
      await loadTaskTimeTrackingSummary(taskId);
      await loadTaskProgressTrackers(taskId);

      // إعادة تحميل المهام الفرعية
      await loadSubtasks(taskId);
      
      // إعادة تحميل الإحصائيات
      await _updateTaskStatistics(taskId);
      
      // إجبار تحديث جميع الواجهات مع تأخير قصير لضمان اكتمال التحديثات
      await Future.delayed(const Duration(milliseconds: 100));
      update(['task_details', 'task_overview', 'task_comments', 'task_contributors', 'task_progress', 'task_attachments']);
      update(); // تحديث عام
      
      // تحديث إضافي بعد تأخير قصير لضمان التحديث الكامل
      await Future.delayed(const Duration(milliseconds: 200));
      update(['task_details', 'task_overview', 'task_contributors']);
      update();
      
      debugPrint('✅ تم تحديث تفاصيل المهمة بالكامل');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث تفاصيل المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إحصائيات المهمة (عدد التعليقات، المرفقات، إلخ)
  Future<void> _updateTaskStatistics(int taskId) async {
    try {
      final task = _currentTask.value;
      if (task == null) return;

      // حساب الإحصائيات الجديدة
      final commentsCount = _taskComments.length;
      final attachmentsCount = task.attachments.length;
      final contributorsCount = _taskContributors.length;

      debugPrint('📊 إحصائيات المهمة $taskId:');
      debugPrint('   - التعليقات: $commentsCount');
      debugPrint('   - المرفقات: $attachmentsCount');
      debugPrint('   - المساهمون: $contributorsCount');

    } catch (e) {
      debugPrint('❌ خطأ في تحديث إحصائيات المهمة: $e');
    }
  }

  /// تحديث فوري للواجهات المطلوبة
  void updateUI({String? source}) {
    debugPrint('🔄 تحديث الواجهة من المصدر: ${source ?? "غير محدد"}');
    update(['task_details']);
    debugPrint('✅ تم تحديث الواجهة بنجاح');
  }

  /// دالة debug لمراقبة التحديثات
  void debugTaskState(String operation) {
    final task = _currentTask.value;
    if (task != null) {
      debugPrint('🔍 حالة المهمة بعد $operation:');
      debugPrint('   - المعرف: ${task.id}');
      debugPrint('   - العنوان: ${task.title}');
      debugPrint('   - التقدم: ${task.completionPercentage}%');
      debugPrint('   - التعليقات: ${task.comments.length}');
      debugPrint('   - المرفقات: ${task.attachments.length}');
      debugPrint('   - المساهمون: ${_taskContributors.length}');
    } else {
      debugPrint('❌ لا توجد مهمة حالية للمراقبة');
    }
  }

  // Progress tracking getters - إضافة getters للمتغيرات الجديدة
  Rx<TaskProgressSummary?> get progressSummary => _progressSummary;
  Rx<TaskTimeTrackingSummary?> get timeTrackingSummary => _timeTrackingSummary;
  List<TimeEntry> get timeEntries => _timeEntries;
  Rx<bool> get isTrackingTime => _isTrackingTime;
  Rx<bool> get isLoadingTimeTracking => _isLoadingTimeTracking;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    // لا نحمل المهام هنا - سيتم تحميلها حسب الصلاحيات عند الحاجة
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      await _cacheService.initialize();
      debugPrint('✅ تم تهيئة خدمات TaskController');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمات TaskController: $e');
    }
  }

  /// تحميل جميع المهام مع التخزين المؤقت
  Future<void> loadAllTasks({bool forceRefresh = true }) async {
    return await AsyncErrorHandler.execute(
      () async {
        _isLoading.value = true;
        _error.value = '';

        debugPrint('🔄 بدء تحميل المهام - forceRefresh: $forceRefresh');

        // 🚀 التحسين: استخدام التحميل الذكي مع التحديث في الخلفية
        if (!forceRefresh) {
          final cachedTasks = await _cacheService.getOrSetWithBackgroundRefresh<List<Task>>(
            CacheKeys.tasks,
            () async => await _apiService.getAllTasks(),
            fromJson: (data) =>
                (data as List).map((item) => Task.fromJson(item)).toList(),
            ttl: const Duration(minutes: 10),
            persistent: true,
            refreshThreshold: const Duration(minutes: 5),
            onBackgroundUpdate: (freshTasks) {
              // تحديث البيانات عند التحديث في الخلفية
              _allTasks.assignAll(freshTasks);
              _applyFilters();
              debugPrint('🔄 تم تحديث المهام في الخلفية: ${freshTasks.length}');
            },
          );

          if (cachedTasks != null && cachedTasks.isNotEmpty) {
            _allTasks.assignAll(cachedTasks);
            _applyFilters();
            debugPrint('📦 تم تحميل ${cachedTasks.length} مهمة من التخزين المؤقت الذكي');
            _isLoading.value = false;
            return;
          }
        }

        debugPrint('🌐 تحميل المهام من API...');

        // تحميل من API إذا لم توجد في التخزين المؤقت أو كان التحديث مطلوباً
        try {
          final tasks = await _apiService.getAllTasks();

          if (tasks.isEmpty) {
            debugPrint('⚠️ لم يتم العثور على مهام في الاستجابة');
            _allTasks.clear();
            _applyFilters();
            return;
          }

          _allTasks.assignAll(tasks);
          
          // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
          await _loadAttachmentsForTasks(tasks);
          
          _applyFilters();

          // حفظ في التخزين المؤقت فقط إذا كانت البيانات صحيحة
          await _cacheService.set(
            CacheKeys.tasks,
            tasks.map((task) => task.toJson()).toList(),
            ttl: const Duration(minutes: 10),
            persistent: true,
          );

          debugPrint(
              '✅ تم تحميل ${tasks.length} مهمة من API وحفظها في التخزين المؤقت');
        } catch (apiError) {
          debugPrint('❌ خطأ في API المهام: $apiError');

          // في حالة خطأ API، نحاول تحميل من التخزين المؤقت كحل بديل
          final fallbackTasks = _cacheService.get<List<Task>>(
            CacheKeys.tasks,
            fromJson: (data) =>
                (data as List).map((item) => Task.fromJson(item)).toList(),
          );

          if (fallbackTasks != null && fallbackTasks.isNotEmpty) {
            _allTasks.assignAll(fallbackTasks);
            _applyFilters();
            debugPrint(
                '🔄 تم تحميل ${fallbackTasks.length} مهمة من التخزين المؤقت كحل بديل');

            // عرض رسالة تحذيرية للمستخدم
            Get.snackbar(
              'تحذير',
              'تم تحميل المهام من التخزين المؤقت. قد تكون البيانات غير محدثة.',
              snackPosition: SnackPosition.TOP,
              backgroundColor: Colors.orange.shade100,
              colorText: Colors.orange.shade800,
              duration: const Duration(seconds: 3),
              icon: const Icon(Icons.warning, color: Colors.orange),
            );
          } else {
            // إذا لم يوجد تخزين مؤقت، نعرض قائمة فارغة
            _allTasks.clear();
            _applyFilters();
            debugPrint('📭 لا توجد مهام متاحة');
          }

          // لا نرمي الخطأ هنا لتجنب توقف التطبيق
          _error.value = 'تعذر تحميل المهام من الخادم';
        }
      },
      errorMessage: 'فشل في تحميل المهام',
      onError: () => _isLoading.value = false,
    );
  }

  /// تحميل مهمة بواسطة المعرف مع التخزين المؤقت
  Future<void> loadTaskById(int id, {bool forceRefresh = false}) async {
    return await AsyncErrorHandler.execute(
      () async {
        _isLoading.value = true;
        _error.value = '';

        // 🚀 التحسين: استخدام التحميل الذكي لتفاصيل المهمة
        if (!forceRefresh) {
          final cachedTask = await _cacheService.getOrSetWithBackgroundRefresh<Task>(
            CacheKeys.taskDetails(id),
            () async {
              final task = await _apiService.getTaskById(id);
              if (task == null) {
                throw AppException(
                  message: 'المهمة غير موجودة',
                  type: ErrorType.notFound,
                );
              }
              return task;
            },
            fromJson: (data) => Task.fromJson(data),
            ttl: const Duration(minutes: 5),
            persistent: true,
            refreshThreshold: const Duration(minutes: 2),
            onBackgroundUpdate: (freshTask) {
              // تحديث المهمة عند التحديث في الخلفية
              _currentTask.value = freshTask;
              debugPrint('🔄 تم تحديث تفاصيل المهمة ${freshTask.title} في الخلفية');
            },
          );

          if (cachedTask != null) {
            _currentTask.value = cachedTask;
            debugPrint('📦 تم تحميل المهمة ${cachedTask.title} من التخزين المؤقت الذكي');
            _isLoading.value = false;
            return;
          }
        }

        // تحميل من API
        final task = await _apiService.getTaskById(id);
        if (task != null) {
          _currentTask.value = task;

          // حفظ في التخزين المؤقت
          await _cacheService.set(
            CacheKeys.taskDetails(id),
            task.toJson(),
            ttl: const Duration(minutes: 5),
            persistent: true,
          );

          debugPrint('🌐 تم تحميل المهمة: ${task.title} من API');
        } else {
          throw AppException(
            message: 'المهمة غير موجودة',
            type: ErrorType.notFound,
          );
        }
      },
      errorMessage: 'فشل في تحميل تفاصيل المهمة',
      onError: () => _isLoading.value = false,
    );
  }

  /// تحميل مهام المستخدم الحالي (المسندة إليه فقط)
  Future<void> loadMyTasks(int userId, {bool forceRefresh = false, Map<String, dynamic>? queryParams}) async {
    try {
      // إذا كان forceRefresh = true، تجاهل الكاش واجلب من السيرفر
      final tasks = await _apiService.getTasksByAssignee(userId,
          queryParams: queryParams, forceRefresh: forceRefresh);
      // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
      await _loadAttachmentsForTasks(tasks);
      _myTasks.assignAll(tasks);
      _allTasks.assignAll(tasks); // تحديث القائمة الرئيسية أيضاً
      debugPrint('تم تحميل [32m[1m[4m${tasks.length}[0m مهمة للمستخدم');
    } catch (e) {
      debugPrint('خطأ في تحميل مهام المستخدم: $e');
    }
  }

  /// تحميل المهام التي يمكن للمستخدم الوصول إليها (من جدول task_access_users)
  Future<void> loadTasksUserCanAccess(int userId, {bool forceRefresh = false, Map<String, dynamic>? queryParams}) async {
    try {
      debugPrint('🔄 تحميل المهام التي يمكن للمستخدم $userId الوصول إليها من الباك إند...');

      // استخدام الدالة الجديدة من الباك إند
      final tasks = await _apiService.getTasksByUserAccess(userId,
          queryParams: queryParams, forceRefresh: forceRefresh);

      // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
      await _loadAttachmentsForTasks(tasks);

      _myTasks.assignAll(tasks);
      _allTasks.assignAll(tasks); // تحديث القائمة الرئيسية أيضاً
      _applyFilters();

      debugPrint('✅ تم تحميل ${tasks.length} مهمة يمكن للمستخدم الوصول إليها من الباك إند');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المهام التي يمكن للمستخدم الوصول إليها: $e');
    }
  }

  /// تحميل المهام المعينة من قبل المستخدم
  Future<void> loadAssignedTasks(int userId) async {
    try {
      final tasks = await _apiService.getTasksByCreator(userId);
      
      // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
      await _loadAttachmentsForTasks(tasks);
      
      _assignedTasks.assignAll(tasks);
      debugPrint('تم تحميل ${tasks.length} مهمة معينة');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المعينة: $e');
    }
  }

  /// تحميل المهام حسب الإدارة - محسن مع دعم forceRefresh
  Future<void> loadTasksByDepartment(int departmentId, {bool forceRefresh = false}) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getTasksByDepartment(departmentId, forceRefresh: forceRefresh);

      // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
      await _loadAttachmentsForTasks(tasks);

      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة للإدارة $departmentId - forceRefresh: $forceRefresh');
    } catch (e) {
      _error.value = 'خطأ في تحميل مهام الإدارة: $e';
      debugPrint('خطأ في تحميل مهام الإدارة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام حسب المكلف بها
  Future<void> loadTasksByAssignee(int assigneeId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getTasksByAssignee(assigneeId);
      
      // تحميل المرفقات للمهام التي لا تحتوي على مرفقات
      await _loadAttachmentsForTasks(tasks);
      
      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة للمكلف $assigneeId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مهام المكلف: $e';
      debugPrint('خطأ في تحميل مهام المكلف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام حسب صلاحيات المستخدم - محسن
  Future<void> loadTasksByUserPermissions(int userId, {bool forceRefresh = false}) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // التحقق من صلاحيات المستخدم
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      final permissionService = Get.find<UnifiedPermissionService>();

      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل دخول');
      }

      debugPrint('🔄 تحميل المهام للمستخدم: ${currentUser.name} (ID: $userId)');
      debugPrint('🔐 فحص الصلاحيات: canViewAllTasks=${permissionService.canViewAllTasks()}, canAdminDepartments=${permissionService.canAdminDepartments()}');

      // استخدام الصلاحيات الديناميكية بدلاً من المنطق الثابت
      if (permissionService.canViewAllTasks()) {
        // المستخدم لديه صلاحية رؤية جميع المهام
        await loadAllTasks(forceRefresh: forceRefresh);
        debugPrint('✅ تم تحميل ${_allTasks.length} مهمة - صلاحية عرض جميع المهام');
      } else if (permissionService.canAdminDepartments() && currentUser.departmentId != null) {
        // المستخدم لديه صلاحية إدارة الأقسام - تحميل مهام إدارته
        await loadTasksByDepartment(currentUser.departmentId!, forceRefresh: forceRefresh);
        debugPrint('✅ تم تحميل ${_allTasks.length} مهمة - صلاحية إدارة الأقسام');
      } else {
        // المستخدم العادي - تحميل المهام التي يمكنه الوصول إليها من جدول task_access_users
        await loadTasksUserCanAccess(userId, forceRefresh: forceRefresh);
        debugPrint('✅ تم تحميل ${_allTasks.length} مهمة - يمكن للمستخدم العادي الوصول إليها');
      }

      // تحديث آخر وقت تحميل
      debugPrint('📅 تم تحديث المهام في: ${DateTime.now()}');

    } catch (e) {
      _error.value = 'خطأ في تحميل المهام حسب الصلاحيات: $e';
      debugPrint('❌ خطأ في تحميل المهام حسب الصلاحيات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام المتأخرة
  Future<void> loadOverdueTasks() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 بدء تحميل المهام المتأخرة...');
      final tasks = await _apiService.getOverdueTasks();

      // تحميل المرفقات للمهام المتأخرة
      await _loadAttachmentsForTasks(tasks);

      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('✅ تم تحميل ${tasks.length} مهمة متأخرة');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام المتأخرة: $e';
      debugPrint('❌ خطأ في تحميل المهام المتأخرة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام المقتربة من الموعد النهائي (48 ساعة أو أقل)
  Future<void> loadTasksDueSoon() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 بدء تحميل المهام المقتربة من الموعد النهائي...');
      final tasks = await _apiService.getTasksDueSoon();

      // تحميل المرفقات للمهام المقتربة
      await _loadAttachmentsForTasks(tasks);

      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('✅ تم تحميل ${tasks.length} مهمة مقتربة من الموعد النهائي');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام المقتربة من الموعد النهائي: $e';
      debugPrint('❌ خطأ في تحميل المهام المقتربة من الموعد النهائي: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على عدد المهام المتأخرة
  Future<int> getOverdueTasksCount() async {
    try {
      final tasks = await _apiService.getOverdueTasks();
      return tasks.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام المتأخرة: $e');
      return 0;
    }
  }

  /// الحصول على عدد المهام المقتربة من الموعد النهائي
  Future<int> getTasksDueSoonCount() async {
    try {
      final tasks = await _apiService.getTasksDueSoon();
      return tasks.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام المقتربة من الموعد النهائي: $e');
      return 0;
    }
  }

  /// تحميل تعليقات المهمة
  Future<void> loadTaskComments(int taskId) async {
    try {
      final comments = await _commentsApiService.getCommentsByTask(taskId);
      _taskComments.assignAll(comments);
      // _comments.assignAll(comments); // تم إزالة السطر المكرر أو الخاطئ
      debugPrint('تم تحميل ${comments.length} تعليق للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل تعليقات المهمة: $e');
    }
  }

  /// تحميل تاريخ المهمة
  Future<void> loadTaskHistory(int taskId) async {
    try {
      final history = await _historyApiService.getHistoryByTask(taskId);
      _taskHistory.assignAll(history);
      update(['task_history']); // تحديث الواجهة
      debugPrint('تم تحميل ${history.length} سجل تاريخ للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل تاريخ المهمة: $e');
    }
  }

  /// تحميل المهام الفرعية
  Future<void> loadSubtasks(int taskId) async {
    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskId);
      _subtasks.assignAll(subtasks);
      debugPrint('تم تحميل ${subtasks.length} مهمة فرعية للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام الفرعية: $e');
    }
  }

  /// تحميل رسائل المهمة
  Future<void> loadTaskMessages(String taskId) async {
    try {
      final taskIdInt = int.tryParse(taskId);
      if (taskIdInt == null) {
        debugPrint('❌ معرف المهمة غير صحيح: $taskId');
        return;
      }

      // محاولة الحصول على الرسائل من التخزين المؤقت أولاً
      final cachedMessages = _cacheService.get<List<Map<String, dynamic>>>(
        CacheKeys.taskMessages(taskIdInt),
        fromJson: (data) => (data as List).cast<Map<String, dynamic>>(),
      );

      if (cachedMessages != null && cachedMessages.isNotEmpty) {
        _taskMessages.assignAll(cachedMessages);
        debugPrint(
            '📦 تم تحميل ${cachedMessages.length} رسالة من التخزين المؤقت');
        return;
      }

      // محاولة تحميل الرسائل من API
      // استخدام خدمة التعليقات كبديل مؤقت حتى تتوفر خدمة الرسائل
      final comments = await _commentsApiService.getCommentsByTask(taskIdInt);

      // تحويل التعليقات إلى تنسيق الرسائل
      final messages = comments
          .map((comment) => {
                'id': comment.id.toString(),
                'taskId': taskIdInt.toString(),
                'senderId': comment.userId.toString(),
                'content': comment.content,
                'timestamp': comment.createdAt,
                'isRead': true,
                'attachments': <Map<String, dynamic>>[],
              })
          .toList();

      _taskMessages.assignAll(messages);

      // حفظ في التخزين المؤقت
      await _cacheService.set(
        CacheKeys.taskMessages(taskIdInt),
        messages,
        ttl: const Duration(minutes: 5),
      );

      debugPrint('✅ تم تحميل ${messages.length} رسالة للمهمة $taskId');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل رسائل المهمة: $e');
      // تعيين قائمة فارغة في حالة الخطأ
      _taskMessages.clear();
    }
  }

  /// تحميل تفاصيل المهمة الكاملة
  Future<void> loadTaskDetails(String taskId, {bool forceRefresh = false}) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) {
      _error.value = 'معرف المهمة غير صحيح';
      Future.microtask(() => update(['task_details']));
      return;
    }

    _isLoading.value = true;
    _error.value = '';
    Future.microtask(() => update(['task_details']));

    try {
      // تحميل المهمة الأساسية أولاً مع خيار forceRefresh
      final task = await _apiService.getTaskById(taskIdInt, forceRefresh: forceRefresh);
      if (task != null) {
        _currentTask.value = task;
        _subtasks.assignAll(task.subtasks);
        _taskComments.assignAll(task.comments);

        // إنهاء التحميل الأساسي
        _isLoading.value = false;
        Future.microtask(() => update(['task_details']));

        // تحميل البيانات الإضافية في الخلفية
        _loadAdditionalTaskData(taskIdInt, taskId);
      } else {
        _error.value = 'لم يتم العثور على المهمة';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل تفاصيل المهمة: $e';
      debugPrint('خطأ في تحميل تفاصيل المهمة: $e');
    } finally {
      _isLoading.value = false;
      Future.microtask(() => update(['task_details']));
    }
  }

  /// تحميل البيانات الإضافية للمهمة في الخلفية
  Future<void> _loadAdditionalTaskData(int taskIdInt, String taskId) async {
    try {
      // تحميل البيانات بشكل متوازي
      await Future.wait([
        loadTaskHistory(taskIdInt).catchError((e) => debugPrint('خطأ في تحميل التاريخ: $e')),
        loadTaskMessages(taskId).catchError((e) => debugPrint('خطأ في تحميل الرسائل: $e')),
        loadTaskContributors(taskIdInt).catchError((e) => debugPrint('خطأ في تحميل المساهمين: $e')),
      ]);
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الإضافية: $e');
    }
  }

  /// البحث في المهام مع تطبيق الصلاحيات
  Future<void> searchTasks(String query) async {
    _searchQuery.value = query;

    if (query.isEmpty) {
      _applyFilters();
      return;
    }

    try {
      // البحث المحلي في المهام المحملة بالفعل (مع تطبيق الصلاحيات)
      final filteredTasks = _allTasks.where((task) {
        final queryLower = query.toLowerCase();

        // البحث في رقم المهمة
        final idMatch = task.id.toString().contains(queryLower);

        // البحث في اسم المهمة
        final titleMatch = task.title.toLowerCase().contains(queryLower);

        // البحث في وصف المهمة
        final descriptionMatch = task.description?.toLowerCase().contains(queryLower) ?? false;

        // البحث في رقم الوارد
        final incomingMatch = task.incoming?.toLowerCase().contains(queryLower) ?? false;

        return idMatch || titleMatch || descriptionMatch || incomingMatch;
      }).toList();

      _filteredTasks.assignAll(filteredTasks);
      debugPrint('🔍 البحث عن "$query": تم العثور على ${filteredTasks.length} مهمة');
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      _applyFilters(); // العودة للمرشحات المحلية في حالة الخطأ
    }
  }

  /// إنشاء مهمة جديدة مع معالجة أخطاء محسنة
  Future<bool> createTask({
    required String title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    String priority = 'medium', // Default: medium priority
    String? status, // حالة المهمة
    int? estimatedTime,
    List<String>?
        accessUserIds, // تعديل نوع المستخدمين بالوصول إلى List<String> لتوافق مع API
    List<File>? attachments, // إضافة مرفقات
    // الحقول الجديدة - New fields
    String? incoming, // الوارد - معلومات إضافية حول مصدر المهمة
    String? note, // ملاحظات - ملاحظات إضافية حول المهمة
  }) async {
    return await AsyncErrorHandler.execute(
          () async {
            _isLoading.value = true;
            _error.value = '';

            // التحقق من صحة البيانات المطلوبة
            if (title.trim().isEmpty) {
              throw AppException(
                message: 'عنوان المهمة مطلوب',
                type: ErrorType.validation,
              );
            }

            // التحقق من وجود البيانات الأساسية أولاً
            await _ensureBasicDataExists();

            final currentUserId = UserHelper.getCurrentUserId();
            if (currentUserId == 0) {
              throw AppException(
                message:
                    'لا يمكن تحديد المستخدم الحالي. يرجى تسجيل الدخول مرة أخرى.',
                type: ErrorType.unauthorized,
              );
            }

            // التأكد من وجود حالة وأولوية صحيحة
            final validStatus = status ?? await _getValidStatus();
            final validPriority = await _getValidPriority(priority);

            if (validStatus.isEmpty || validPriority.isEmpty) {
              throw AppException(
                message: 'لا توجد حالات أو أولويات صحيحة للمهام',
                type: ErrorType.serverError,
              );
            }

            // تحويل قائمة معرفات المستخدمين من int إلى String إذا كانت موجودة
            List<String>? accessUserIdsStr;
            if (accessUserIds != null) {
              accessUserIdsStr =
                  accessUserIds.map((id) => id.toString()).toList();
            }

            // إنشاء بيانات المهمة للـ API
            final taskData = Task(
              id: 0, // سيتم تعيينه من الخادم
              title: title.trim(),
              description: description?.trim(),
              taskTypeId: taskTypeId,
              creatorId: currentUserId,
              assigneeId: assigneeId,
              departmentId: departmentId,
              createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
              startDate: startDate != null
                  ? startDate.millisecondsSinceEpoch ~/ 1000
                  : null,
              dueDate: dueDate != null
                  ? dueDate.millisecondsSinceEpoch ~/ 1000
                  : null,
              status: validStatus,
              priority: validPriority,
              completionPercentage: 0,
              estimatedTime: estimatedTime,
              isDeleted: false,
              // الحقول الجديدة - New fields
              incoming: incoming?.trim(),
              note: note?.trim(),
              accessUserIds:
                  accessUserIdsStr, // تمرير المستخدمين بالوصول كقائمة String
            );

            debugPrint('🚀 إنشاء مهمة بالبيانات: ${taskData.toJson()}');

            final newTask = await _apiService.createTask(taskData);
            if (newTask != null) {
              _allTasks.add(newTask);
              _currentTask.value = newTask;
              _applyFilters();

              // إزالة التخزين المؤقت للمهام لإجبار التحديث
              await _cacheService.remove(CacheKeys.tasks);

              debugPrint('✅ تم إنشاء المهمة بنجاح: ${newTask.title}');
              debugPrint('📋 معرف المهمة الجديد: ${newTask.id}');

              // رفع المرفقات إذا وجدت - بشكل متزامن لضمان اكتمالها قبل الانتقال
              if (attachments != null && attachments.isNotEmpty) {
                debugPrint('📤 بدء رفع ${attachments.length} مرفق للمهمة الجديدة ${newTask.id}');

                // رفع المرفقات بشكل متتالي لضمان الاستقرار
                for (int i = 0; i < attachments.length; i++) {
                  final file = attachments[i];
                  debugPrint('📤 رفع المرفق ${i + 1}/${attachments.length}: ${file.path}');

                  try {
                    final uploadedAttachment = await addAttachment(newTask.id, currentUserId, file);
                    if (uploadedAttachment != null) {
                      debugPrint('✅ تم رفع المرفق ${i + 1}: ${uploadedAttachment.fileName}');
                    } else {
                      debugPrint('❌ فشل في رفع المرفق ${i + 1}: ${file.path}');
                    }
                  } catch (e) {
                    debugPrint('❌ خطأ في رفع المرفق ${i + 1}: $e');
                  }
                }

                // تحديث نهائي للمهمة مع المرفقات المرفوعة
                debugPrint('🔄 تحديث نهائي للمهمة مع المرفقات...');
                await loadTaskById(newTask.id, forceRefresh: true);

                // تحديث شامل للواجهة
                update(['task_details', 'task_attachments', 'task_overview']);
                refresh();

                debugPrint('✅ تم إنشاء المهمة مع ${attachments.length} مرفق بنجاح');
              }

              // عرض رسالة نجاح
              Get.snackbar(
                'نجح',
                'تم إنشاء المهمة "${newTask.title}" بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green.shade100,
                colorText: Colors.green.shade800,
                duration: const Duration(seconds: 3),
                icon: const Icon(Icons.check_circle, color: Colors.green),
              );

              return true;
            } else {
              throw AppException(
                message:
                    'فشل في إنشاء المهمة - لم يتم إرجاع بيانات المهمة من الخادم',
                type: ErrorType.serverError,
              );
            }
          },
          errorMessage: 'فشل في إنشاء المهمة',
          onError: () => _isLoading.value = false,
        ) ??
        false;
  }

  /// تحديث مهمة مع معالجة أخطاء محسنة
  Future<bool> updateTask({
    required int id,
    String? title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    DateTime? completedAt,
    String? status,
    String? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
    // الحقول الجديدة - New fields
    String? incoming, // الوارد
    String? note, // الملاحظات
  }) async {
    return await AsyncErrorHandler.execute(
          () async {
            _isLoading.value = true;
            _error.value = '';

            // التحقق من صحة البيانات
            if (title != null && title.trim().isEmpty) {
              throw AppException(
                message: 'عنوان المهمة لا يمكن أن يكون فارغاً',
                type: ErrorType.validation,
              );
            }

            // الحصول على المهمة الحالية أولاً
            final currentTask =
                _allTasks.firstWhereOrNull((task) => task.id == id);
            if (currentTask == null) {
              throw AppException(
                message: 'المهمة غير موجودة',
                type: ErrorType.notFound,
              );
            }

            // إنشاء المهمة المحدثة
            final taskToUpdate = currentTask.copyWith(
              title: title?.trim(),
              description: description?.trim(),
              taskTypeId: taskTypeId,
              assigneeId: assigneeId,
              departmentId: departmentId,
              startDate: startDate != null
                  ? startDate.millisecondsSinceEpoch ~/ 1000
                  : null,
              dueDate: dueDate != null
                  ? dueDate.millisecondsSinceEpoch ~/ 1000
                  : null,
              completedAt: completedAt != null
                  ? completedAt.millisecondsSinceEpoch ~/ 1000
                  : null,
              status: status,
              priority: priority,
              completionPercentage: completionPercentage,
              estimatedTime: estimatedTime,
              actualTime: actualTime,
              // الحقول الجديدة - New fields
              incoming: incoming?.trim(),
              note: note?.trim(),
            );

            debugPrint('🔄 تحديث المهمة $id: ${taskToUpdate.toJson()}');

            final updatedTask = await _apiService.updateTask(taskToUpdate);
            if (updatedTask != null) {
              final index = _allTasks.indexWhere((task) => task.id == id);
              if (index != -1) {
                _allTasks[index] = updatedTask;
              }

              _currentTask.value = updatedTask;

              _applyFilters();

              // تحديث التخزين المؤقت
              await _cacheService.set(
                CacheKeys.taskDetails(id),
                updatedTask.toJson(),
                ttl: const Duration(minutes: 5),
                persistent: true,
              );

              // إزالة قائمة المهام من التخزين المؤقت لإجبار التحديث
              await _cacheService.remove(CacheKeys.tasks);

              debugPrint('✅ تم تحديث المهمة: ${updatedTask.title}');

              // عرض رسالة نجاح
              Get.snackbar(
                'تم التحديث',
                'تم تحديث المهمة "${updatedTask.title}" بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.blue.shade100,
                colorText: Colors.blue.shade800,
                duration: const Duration(seconds: 2),
                icon: const Icon(Icons.check_circle, color: Colors.green),
              );

              return true;
            } else {
              throw AppException(
                message:
                    'فشل في تحديث المهمة - لم يتم إرجاع بيانات محدثة من الخادم',
                type: ErrorType.serverError,
              );
            }
          },
          errorMessage: 'فشل في تحديث المهمة',
          onError: () => _isLoading.value = false,
        ) ??
        false;
  }

  /// حذف مهمة
  Future<bool> deleteTask(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.deleteTask(id);
      if (success) {
        _allTasks.removeWhere((task) => task.id == id);
        if (_currentTask.value?.id == id) {
          _currentTask.value = null;
        }
        _applyFilters();
        debugPrint('تم حذف المهمة');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المهمة: $e';
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تغيير حالة المهمة
  Future<bool> changeTaskStatus(int id, String status) async {
    return await updateTask(
      id: id,
      status: status,
      completedAt: status == 'completed'
          ? DateTime.now()
          : null,
    );
  }

  /// تعيين مهمة لمستخدم
  Future<bool> assignTask(int taskId, int assigneeId) async {
    return await updateTask(
      id: taskId,
      assigneeId: assigneeId,
    );
  }

  /// تحويل المهمة إلى مستخدم آخر مع تسجيل التاريخ والمساهمة
  Future<bool> transferTask(int taskId, int newAssigneeId, String comment, List<String> attachments) async {
    try {
      _isLoading.value = true;
      
      // 1. الحصول على المستخدم الحالي
      final currentUserId = UserHelper.getCurrentUserId();
      if (currentUserId <= 0) {
        throw Exception('يجب تسجيل الدخول لتحويل المهمة');
      }
      
      // 2. استخدام نقطة نهاية API المخصصة للتحويل
      final updatedTask = await _apiService.transferTask(
        taskId,
        newAssigneeId,
        currentUserId,
        comment,
        attachments,
      );
      
      if (updatedTask == null) {
        throw Exception('فشل في تحويل المهمة');
      }
      
      // 3. تحديث المهمة الحالية في الذاكرة
      _currentTask.value = updatedTask;
      
      // 4. إعادة تحميل بيانات المهمة بالكامل
      await loadTaskDetails(taskId.toString(), forceRefresh: true);

      // 5. إعادة تحميل التعليقات لتحديث عدد التعليقات
      try {
        if (Get.isRegistered<SimpleTaskCommentsController>()) {
          final commentsController = Get.find<SimpleTaskCommentsController>();
          await commentsController.loadComments(taskId);
        }
      } catch (e) {
        debugPrint('تحذير: لم يتم العثور على SimpleTaskCommentsController: $e');
      }

      // 6. إعادة تحميل المساهمين لتحديث قائمة المساهمين
      await loadTaskContributors(taskId);

      // 7. إعادة تحميل تاريخ المهمة
      await loadTaskHistory(taskId);

      // 8. تحديث تلقائي للتقدم عند تحويل المهمة (بدون إعادة تعيين)
      try {
        debugPrint('🔄 بدء التحديث التلقائي للتقدم عند تحويل المهمة...');
        final progressUpdated = await autoUpdateTaskProgress(
          taskId,
          currentUserId,
          'transfer',
          actionDescription: 'تم تحويل المهمة إلى مستخدم آخر',
          customIncrement: 2.0, // زيادة صغيرة لتسجيل التحويل
        );

        if (progressUpdated) {
          debugPrint('✅ تم تحديث التقدم بنجاح عند تحويل المهمة');
        } else {
          debugPrint('⚠️ لم يتم تحديث التقدم عند تحويل المهمة');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في التحديث التلقائي للتقدم عند تحويل المهمة: $e');
      }

      // 9. تحديث الواجهة بشكل شامل
      update(['task_details', 'task_overview', 'task_history', 'task_contributors', 'task_comments', 'task_attachments', 'task_progress']);
      
      // 6. عرض رسالة نجاح
      Get.snackbar(
        'تم بنجاح',
        'تم تحويل المهمة بنجاح إلى المستخدم الجديد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.green),
      );
      
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحويل المهمة: $e');
      Get.snackbar(
        'خطأ',
        'فشل في تحويل المهمة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة تعليق على المهمة
  Future<bool> addTaskComment(int taskId, String content) async {
    try {
      if (!UserHelper.isLoggedIn()) {
        debugPrint('خطأ: لا يوجد مستخدم مسجل دخول');
        return false;
      }

      final currentUserId = UserHelper.getCurrentUserId();
      final comment = TaskComment(
        id: 0, // سيتم تعيينه من قبل الخادم
        taskId: taskId,
        userId: currentUserId,
        content: content,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isDeleted: false,
      );

      final createdComment =
          await _commentsApiService.createTaskComment(comment);

      // تحديث قائمة التعليقات المحلية
      _taskComments.add(createdComment);

      // تحديث المهمة الحالية إذا كانت نفس المهمة
      if (_currentTask.value?.id == taskId) {
        final updatedComments = List<TaskComment>.from(_currentTask.value!.comments);
        updatedComments.add(createdComment);
        _currentTask.value = _currentTask.value!.copyWith(comments: updatedComments);
        _currentTask.refresh();
      }

      // تحديث المهمة في قائمة جميع المهام
      final taskIndex = _allTasks.indexWhere((t) => t.id == taskId);
      if (taskIndex != -1) {
        final updatedComments = List<TaskComment>.from(_allTasks[taskIndex].comments);
        updatedComments.add(createdComment);
        _allTasks[taskIndex] = _allTasks[taskIndex].copyWith(comments: updatedComments);
        _applyFilters();
      }

      // إعادة تحميل المساهمين لأن إضافة تعليق قد يضيف مساهم جديد
      await loadTaskContributors(taskId);

      // تحديث تلقائي للتقدم عند إضافة تعليق
      final userId = UserHelper.getCurrentUserId();
      debugPrint('🔄 بدء التحديث التلقائي للتقدم عند إضافة تعليق...');

      final progressUpdated = await autoUpdateTaskProgress(
        taskId,
        userId,
        'comment',
        actionDescription: 'تم إضافة تعليق: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}',
      );

      if (progressUpdated) {
        debugPrint('✅ تم تحديث التقدم بنجاح عند إضافة التعليق');
      } else {
        debugPrint('⚠️ لم يتم تحديث التقدم عند إضافة التعليق');
      }

      // تحديث فوري وشامل للواجهة
      update(['task_details', 'task_overview', 'task_progress', 'task_comments']);
      refresh();
      updateUI(source: 'إضافة تعليق');

      // مراقبة الحالة
      debugTaskState('إضافة تعليق');

      debugPrint('تم إضافة التعليق بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      return false;
    }
  }

  /// تحديث أولوية المهمة
  Future<bool> updateTaskPriority(int taskId, int userId, String priority) async {
    try {
      debugPrint(
          '🔄 محاولة تحديث أولوية المهمة $taskId إلى الأولوية $priority');

      // التحقق من صحة الأولوية أولاً
      final validPriority = await _getValidPriority(priority);
      if (validPriority.isEmpty) {
        debugPrint('❌ الأولوية $priority غير صحيحة');
        throw AppException(
          message: 'الأولوية المحددة غير صحيحة: $priority',
          type: ErrorType.validation,
        );
      }

      debugPrint('✅ الأولوية $validPriority صحيحة، جاري التحديث...');

      return await updateTask(
        id: taskId,
        priority: validPriority,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أولوية المهمة: $e');

      // عرض رسالة خطأ للمستخدم
      Get.snackbar(
        'خطأ',
        'فشل في تحديث أولوية المهمة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.red),
      );

      return false;
    }
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(int taskId, int userId, String status) async {
    return await updateTask(
      id: taskId,
      status: status,
      completedAt:
          status == 'completed' ? DateTime.now() : null,
    );
  }

  /// تحديث تقدم المهمة
  Future<bool> updateTaskProgress(
    int taskId,
    int userId,
    double progressPercentage, {
    String? notes,
    String? evidenceType,
    String? evidenceDescription,
    int? attachmentId,
  }) async {
    try {
      debugPrint('🔄 بدء تحديث تقدم المهمة $taskId إلى $progressPercentage%');

      // استخدام خدمة TaskProgressTrackers لتحديث التقدم مع تمرير معرف المستخدم
      await _progressTrackersApiService.updateTaskProgress(
        taskId,
        progressPercentage,
        notes ?? '',
        updatedBy: userId,
      );

      debugPrint('✅ تم تحديث التقدم في قاعدة البيانات بنجاح');

      // تحديث فوري للمهمة الحالية في الذاكرة قبل إعادة التحميل
      final currentTask = _currentTask.value;
      if (currentTask != null && currentTask.id == taskId) {
        final updatedTask = currentTask.copyWith(
          completionPercentage: progressPercentage.toInt(),
        );
        _currentTask.value = updatedTask;

        // تحديث المهمة في القائمة الرئيسية أيضاً
        final taskIndex = _allTasks.indexWhere((t) => t.id == taskId);
        if (taskIndex != -1) {
          _allTasks[taskIndex] = updatedTask;
          _applyFilters();
        }
      }

      // تحديث فوري للواجهة
      update(['task_details', 'task_overview', 'task_progress']);
      refresh();

      // إعادة تحميل تفاصيل المهمة لضمان التحديث الشامل (في الخلفية)
      Future.delayed(const Duration(milliseconds: 100), () {
        refreshTaskDetails(taskId);
      });

      debugPrint('✅ تم تحديث تقدم المهمة والواجهة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث تقدم المهمة: $e');
      return false;
    }
  }

  /// إعدادات التحديث التلقائي للتقدم
  static const Map<String, double> _autoProgressIncrements = {
    'comment': 3.0,      // +3% لكل تعليق
    'attachment': 5.0,   // +5% لكل مرفق
    'document': 10.0,    // +10% لكل مستند
    'subtask': 15.0,     // +15% لكل مهمة فرعية
    'transfer': 2.0,     // +2% لكل تحويل مهمة
  };

  /// الحصول على نسبة التحديث التلقائي لنوع نشاط معين
  static double getAutoProgressIncrement(String actionType) {
    return _autoProgressIncrements[actionType] ?? 0.0;
  }

  /// الحصول على جميع إعدادات التحديث التلقائي
  static Map<String, double> getAllAutoProgressIncrements() {
    return Map.from(_autoProgressIncrements);
  }

  /// التحقق من إمكانية التحديث التلقائي لنوع نشاط معين
  static bool canAutoUpdateProgress(String actionType) {
    return _autoProgressIncrements.containsKey(actionType) &&
           _autoProgressIncrements[actionType]! > 0;
  }

  /// حساب تقدم المهام الفرعية وتضمينها في التقدم الإجمالي
  Future<double> calculateSubtasksProgress(int taskId) async {
    try {
      // تحميل المهام الفرعية للمهمة
      final subtasksController = Get.find<SubtasksController>();
      await subtasksController.getSubtasksByTask(taskId);

      final subtasks = subtasksController.taskSubtasks;
      if (subtasks.isEmpty) {
        debugPrint('📊 لا توجد مهام فرعية للمهمة $taskId');
        return 0.0;
      }

      // حساب نسبة المهام الفرعية المكتملة
      final completedSubtasks = subtasks.where((s) => s.isCompleted).length;
      final totalSubtasks = subtasks.length;
      final subtasksProgressPercentage = (completedSubtasks / totalSubtasks) * 100;

      debugPrint('📊 تقدم المهام الفرعية: $completedSubtasks/$totalSubtasks = ${subtasksProgressPercentage.toStringAsFixed(1)}%');

      return subtasksProgressPercentage;
    } catch (e) {
      debugPrint('❌ خطأ في حساب تقدم المهام الفرعية: $e');
      return 0.0;
    }
  }

  /// حساب التقدم الإجمالي مع تضمين المهام الفرعية
  Future<double> calculateTotalProgressWithSubtasks(int taskId) async {
    try {
      // الحصول على التقدم الحالي من قاعدة البيانات
      final currentProgress = await _progressTrackersApiService.getCurrentTaskProgress(taskId) ?? 0.0;

      // حساب تقدم المهام الفرعية
      final subtasksProgress = await calculateSubtasksProgress(taskId);

      // حساب التقدم الإجمالي (70% للتقدم اليدوي + 30% للمهام الفرعية)
      final totalProgress = (currentProgress * 0.7) + (subtasksProgress * 0.3);

      debugPrint('📊 التقدم الإجمالي: ${currentProgress.toStringAsFixed(1)}% (يدوي) + ${subtasksProgress.toStringAsFixed(1)}% (فرعية) = ${totalProgress.toStringAsFixed(1)}%');

      return totalProgress.clamp(0.0, 100.0);
    } catch (e) {
      debugPrint('❌ خطأ في حساب التقدم الإجمالي: $e');
      return 0.0;
    }
  }

  /// تحديث تلقائي لتقدم المهمة بناءً على نوع النشاط
  Future<bool> autoUpdateTaskProgress(
    int taskId,
    int userId,
    String actionType, {
    String? actionDescription,
    double? customIncrement,
    bool enableAutoUpdate = true, // إمكانية تعطيل التحديث التلقائي
  }) async {
    try {
      // التحقق من تفعيل التحديث التلقائي
      if (!enableAutoUpdate) {
        debugPrint('⚠️ التحديث التلقائي للتقدم معطل');
        return false;
      }

      // تحديد نسبة الزيادة بناءً على نوع النشاط
      final increment = customIncrement ?? _autoProgressIncrements[actionType] ?? 0.0;

      if (increment <= 0) {
        debugPrint('⚠️ لا توجد زيادة محددة لنوع النشاط: $actionType');
        return false;
      }

      // البحث عن أكبر قيمة تقدم موجودة في قاعدة البيانات
      debugPrint('🔍 البحث عن أحدث تقدم للمهمة $taskId...');
      final latestProgress = await _progressTrackersApiService.getCurrentTaskProgress(taskId);

      // إذا لم توجد قيم سابقة، نبدأ من 0
      final currentProgress = latestProgress ?? 0.0;
      debugPrint('📊 التقدم الحالي: $currentProgress%');

      // حساب التقدم الجديد (مع ضمان عدم تجاوز 100%)
      final newProgress = (currentProgress + increment).clamp(0.0, 100.0);

      // إذا كان التقدم الجديد مساوياً للحالي، لا نحدث
      if (newProgress == currentProgress) {
        debugPrint('⚠️ التقدم وصل للحد الأقصى أو لا يحتاج تحديث');
        return false;
      }

      // إنشاء ملاحظة تلقائية
      final actionNames = {
        'comment': 'إضافة تعليق',
        'attachment': 'رفع مرفق',
        'document': 'إنشاء مستند',
        'subtask': 'إكمال مهمة فرعية',
      };

      final actionName = actionNames[actionType] ?? 'نشاط';
      final autoNotes = actionDescription ??
          'تحديث تلقائي (+${increment.toInt()}%) - $actionName';

      debugPrint('🔄 تحديث تلقائي للتقدم: $currentProgress% → $newProgress%');
      debugPrint('📝 السبب: $autoNotes');

      // تحديث التقدم في قاعدة البيانات
      final success = await updateTaskProgress(
        taskId,
        userId,
        newProgress,
        notes: autoNotes,
        evidenceType: actionType,
        evidenceDescription: actionDescription,
      );

      if (success) {
        // تحديث فوري للمهمة الحالية في الذاكرة
        final currentTask = _currentTask.value;
        if (currentTask != null && currentTask.id == taskId) {
          final updatedTask = currentTask.copyWith(
            completionPercentage: newProgress.toInt(),
          );
          _currentTask.value = updatedTask;

          // تحديث المهمة في القائمة الرئيسية أيضاً
          final taskIndex = _allTasks.indexWhere((t) => t.id == taskId);
          if (taskIndex != -1) {
            _allTasks[taskIndex] = updatedTask;
            _applyFilters();
          }
        }

        // تحديث فوري وشامل للواجهة
        update(['task_details', 'task_overview', 'task_progress']);
        refresh();

        debugPrint('✅ تم التحديث التلقائي للتقدم بنجاح وتحديث الواجهة');
      } else {
        debugPrint('❌ فشل في التحديث التلقائي للتقدم');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في التحديث التلقائي للتقدم: $e');
      return false;
    }
  }

  /// تحميل مرفقات مهمة واحدة وتحديث المهمة الحالية والقائمة الرئيسية
  /// مفيدة بعد عمليات رفع الملفات أو استقبال إشعارات SignalR
  Future<void> loadTaskAttachments(int taskId) async {
    try {
      debugPrint('🔗 بدء تحميل مرفقات المهمة $taskId...');

      // تحميل المرفقات من الخادم
      final attachments = await _attachmentsApiService.getAttachmentsByTask(taskId);

      // تحديث المهمة بالمرفقات الجديدة (حتى لو كانت قائمة فارغة)
      // تحديث المهمة الحالية إذا كانت نفس المهمة
      if (_currentTask.value != null && _currentTask.value!.id == taskId) {
        debugPrint('🔄 تحديث المهمة الحالية - العدد القديم: ${_currentTask.value!.attachments.length}, العدد الجديد: ${attachments.length}');

        // تحديث المهمة باستخدام copyWith مع إنشاء نسخة جديدة من القائمة
        final updatedTask = _currentTask.value!.copyWith(
          attachments: List<Attachment>.from(attachments), // إنشاء نسخة جديدة من القائمة
        );

        // إجبار GetX على اكتشاف التغيير
        _currentTask.value = null;
        _currentTask.value = updatedTask;

        // تحقق فوري من التحديث
        final actualCount = _currentTask.value!.attachments.length;
        debugPrint('✅ تم تحديث مرفقات المهمة الحالية $taskId');
        debugPrint('🔍 التحقق الفوري - المتوقع: ${attachments.length}, الفعلي: $actualCount');

        if (actualCount == attachments.length) {
          debugPrint('✅ التحديث نجح بشكل صحيح!');
          debugPrint('📎 أسماء المرفقات: ${_currentTask.value!.attachments.map((a) => a.fileName).join(', ')}');

          // تحديث فوري وشامل للواجهة بعد التحديث الناجح
          update(['task_details', 'task_attachments', 'task_overview']);
          update(); // تحديث عام
          refresh(); // إشعار جميع المستمعين
        } else {
          debugPrint('❌ فشل التحديث - إعادة المحاولة بطريقة مختلفة...');

          // طريقة بديلة: إنشاء مهمة جديدة تماماً
          final currentTask = _currentTask.value!;
          final newTask = Task(
            id: currentTask.id,
            title: currentTask.title,
            description: currentTask.description,
            taskTypeId: currentTask.taskTypeId,
            creatorId: currentTask.creatorId,
            assigneeId: currentTask.assigneeId,
            departmentId: currentTask.departmentId,
            createdAt: currentTask.createdAt,
            startDate: currentTask.startDate,
            dueDate: currentTask.dueDate,
            completedAt: currentTask.completedAt,
            status: currentTask.status,
            priority: currentTask.priority,
            completionPercentage: currentTask.completionPercentage,
            estimatedTime: currentTask.estimatedTime,
            actualTime: currentTask.actualTime,
            isDeleted: currentTask.isDeleted,
            incoming: currentTask.incoming,
            note: currentTask.note,
            creator: currentTask.creator,
            assignee: currentTask.assignee,
            department: currentTask.department,
            taskType: currentTask.taskType,
            statusNavigation: currentTask.statusNavigation,
            priorityNavigation: currentTask.priorityNavigation,
            subtasks: currentTask.subtasks,
            comments: currentTask.comments,
            attachments: List<Attachment>.from(attachments), // المرفقات الجديدة
            accessUserIds: currentTask.accessUserIds,
          );

          // إجبار GetX على اكتشاف التغيير
          _currentTask.value = null;
          _currentTask.value = newTask;
          debugPrint('🔄 إعادة المحاولة - العدد الآن: ${_currentTask.value!.attachments.length}');

          // تحديث شامل للواجهة بعد إعادة المحاولة
          update(['task_details', 'task_attachments', 'task_overview']);
          update(); // تحديث عام
          refresh(); // إشعار جميع المستمعين
        }
      } else {
        debugPrint('⚠️ المهمة الحالية غير متطابقة أو فارغة - taskId: $taskId, currentTaskId: ${_currentTask.value?.id}');
      }

      // تحديث القائمة الرئيسية أيضاً
      final taskIndex = _allTasks.indexWhere((t) => t.id == taskId);
      if (taskIndex != -1) {
        final oldTask = _allTasks[taskIndex];
        final updatedTaskForList = oldTask.copyWith(attachments: List<Attachment>.from(attachments));

        // إجبار GetX على اكتشاف التغيير في القائمة
        final tempList = List<Task>.from(_allTasks);
        tempList[taskIndex] = updatedTaskForList;
        _allTasks.assignAll(tempList);

        _applyFilters(); // إعادة تطبيق المرشحات

        // تحقق من تحديث القائمة الرئيسية
        final updatedTaskInList = _allTasks[taskIndex];
        debugPrint('✅ تم تحديث مرفقات المهمة في القائمة الرئيسية');
        debugPrint('🔍 القائمة الرئيسية - المتوقع: ${attachments.length}, الفعلي: ${updatedTaskInList.attachments.length}');
      } else {
        debugPrint('⚠️ لم يتم العثور على المهمة $taskId في القائمة الرئيسية');
        // لا نعيد تحميل المهام لتجنب التحديثات المتكررة
      }

      // تحديث واجهة المستخدم مع تأخير قصير لضمان التحديث
      update(['task_details', 'task_attachments', 'task_overview']);
      update(); // تحديث عام

      // تحديث إضافي بعد تأخير قصير لضمان تحديث الواجهة
      await Future.delayed(const Duration(milliseconds: 200));
      update(['task_details', 'task_attachments', 'task_overview']);

      // تحديث نهائي للتأكد من تحديث جميع الواجهات
      await Future.delayed(const Duration(milliseconds: 300));
      update();
      
      // حفظ في التخزين المؤقت إذا كانت المهمة الحالية
      if (_currentTask.value != null && _currentTask.value!.id == taskId) {
        await _cacheService.set(
          CacheKeys.taskDetails(taskId),
          _currentTask.value!.toJson(),
          ttl: const Duration(minutes: 10),
        );
      }
      
      debugPrint('✅ تم تحديث مرفقات المهمة $taskId بنجاح (${attachments.length} مرفق)');
      
      // تحقق نهائي من التحديث
      if (_currentTask.value != null) {
        final currentAttachmentsCount = _currentTask.value!.attachments.length;
        debugPrint('🔍 التحقق النهائي - المهمة الحالية تحتوي على $currentAttachmentsCount مرفق');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مرفقات المهمة $taskId: $e');
      _error.value = 'فشل في تحميل مرفقات المهمة';
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = List<Task>.from(_allTasks);

    // تطبيق مرشح الحالة
    if (_statusFilter.value != null) {
      filtered = filtered
          .where((task) => task.status == _statusFilter.value!)
          .toList();
    }

    // تطبيق مرشح الأولوية
    if (_priorityFilter.value != null) {
      filtered = filtered
          .where((task) => task.priority == _priorityFilter.value!)
          .toList();
    }

    // تطبيق مرشح المعين إليه
    if (_assigneeFilter.value != null) {
      filtered = filtered
          .where((task) => task.assigneeId == _assigneeFilter.value)
          .toList();
    }

    // تطبيق مرشح القسم
    if (_departmentFilter.value != null) {
      filtered = filtered
          .where((task) => task.departmentId == _departmentFilter.value)
          .toList();
    }

    // تطبيق مرشح المهام المتأخرة
    if (_showOverdueOnly.value) {
      filtered = filtered.where((task) => task.isOverdue()).toList();
    }

    // تطبيق مرشح البحث المحلي
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered
          .where((task) =>
              task.title
                  .toLowerCase()
                  .contains(_searchQuery.value.toLowerCase()) ||
              (task.description
                      ?.toLowerCase()
                      .contains(_searchQuery.value.toLowerCase()) ??
                  false))
          .toList();
    }

    _filteredTasks.assignAll(filtered);
  }

  /// تعيين مرشحات
  void setStatusFilter(String? status) {
    _statusFilter.value = status;
    _applyFilters();
  }

  void setPriorityFilter(String? priority) {
    _priorityFilter.value = priority;
    _applyFilters();
  }

  void setAssigneeFilter(int? assigneeId) {
    _assigneeFilter.value = assigneeId;
    _applyFilters();
  }

  void setDepartmentFilter(int? departmentId) {
    _departmentFilter.value = departmentId;
    _applyFilters();
  }

  void setOverdueFilter(bool showOverdueOnly) {
    _showOverdueOnly.value = showOverdueOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _statusFilter.value = null;
    _priorityFilter.value = null;
    _assigneeFilter.value = null;
    _departmentFilter.value = null;
    _showOverdueOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
    // _errorMessage.value = ''; // تم إزالة السطر الذي يستخدم متغير غير معرف
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    // استخدام الدالة الموحدة بدلاً من loadAllTasks
    final authController = Get.find<AuthController>();
    if (authController.currentUser.value != null) {
      await loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);
    }
  }

  /// الحصول على إحصائيات المهام
  Map<String, int> get taskStats {
    return {
      'total': _allTasks.length,
      'pending': _allTasks.where((t) => t.status == 'pending').length,
      'inProgress': _allTasks.where((t) => t.status == 'in_progress').length,
      'completed': _allTasks.where((t) => t.status == 'completed').length,
      'overdue': _allTasks.where((t) => t.isOverdue()).length,
    };
  }

  /// الحصول على عدد المهام الفرعية
  Future<int> getSubtasksCount(String taskId) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return 0;

    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskIdInt);
      return subtasks.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام الفرعية: $e');
      return 0;
    }
  }

  /// الحصول على عدد المهام الفرعية المكتملة
  Future<int> getCompletedSubtasksCount(String taskId) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return 0;

    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskIdInt);
      return subtasks.where((s) => s.isCompleted).length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام الفرعية المكتملة: $e');
      return 0;
    }
  }

  /// الاشتراك في تحديثات الرسائل
  Worker subscribeToMessagesUpdates(VoidCallback callback) {
    return ever(_taskMessages, (_) => callback());
  }

  /// تحديث رسالة الخطأ
  void setError(String message) {
    _error.value = message;
  }

  // دوال إضافية مطلوبة للتوافق مع الشاشات

  /// التحقق من وجود البيانات الأساسية وإنشاؤها إذا لزم الأمر
  Future<void> _ensureBasicDataExists() async {
    // لا حاجة لأي تحقق من API، القيم ثابتة في enum
    debugPrint('✅ جميع بيانات الحالات والأولويات متوفرة محلياً (enum)');
  }

  /// الحصول على حالة صحيحة للمهمة
  Future<String> _getValidStatus() async {
    // استخدم أول حالة من enum أو قيمة افتراضية
    return task_enums.TaskStatus.pending.stringValue;
  }

  /// الحصول على أولوية صحيحة للمهمة
  Future<String> _getValidPriority(String requestedPriority) async {
    // تحقق من وجود الأولوية في enum
    if (task_enums.TaskPriority.isValid(requestedPriority)) {
      return requestedPriority;
    }
    return task_enums.TaskPriority.medium.stringValue;
  }



  /// تحميل بيانات تتبع الوقت للمهمة
  Future<void> loadTaskTimeTracking(int taskId) async {
    try {
      debugPrint('🔄 تحميل بيانات تتبع الوقت للمهمة $taskId...');

      // تحميل سجلات تتبع الوقت من API
      final entries = await _timeTrackingApiService.getTimeEntriesByTask(taskId);

      _timeEntries.clear();
      _timeEntries.addAll(entries);

      // تحديث حالة التتبع بناءً على وجود سجلات نشطة
      _isTrackingTime.value = entries.any((entry) => entry.endTime == null);

      debugPrint('✅ تم تحميل ${_timeEntries.length} سجل تتبع وقت للمهمة $taskId');
      debugPrint('🔄 حالة التتبع: ${_isTrackingTime.value ? "نشط" : "غير نشط"}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات تتبع الوقت: $e');
      _timeEntries.clear();
    }
  }



  /// بدء تتبع الوقت
  Future<void> startTimeTracking(int taskId, int userId,
      {String? description}) async {
    try {
      debugPrint('🔄 بدء تتبع الوقت للمهمة $taskId للمستخدم $userId');

      // بدء تتبع الوقت من API
      final entry = await _timeTrackingApiService.startTimeTracking(
        taskId,
        description ?? 'العمل على المهمة',
        userId: userId,
      );

      if (entry != null) {
        _timeEntries.add(entry);
        _isTrackingTime.value = true;
        debugPrint('✅ تم بدء تتبع الوقت بنجاح - ID: ${entry.id}');

        // إعادة تحميل بيانات تتبع الوقت
        await loadTaskTimeTracking(taskId);
        await loadTaskTimeTrackingSummary(taskId);

        // تحديث الواجهة
        updateUI(source: 'بدء تتبع الوقت');
      }
    } catch (e) {
      debugPrint('❌ خطأ في بدء تتبع الوقت: $e');
      rethrow;
    }
  }

  /// إنهاء تتبع الوقت
  Future<void> endTimeTracking(int entryId) async {
    try {
      debugPrint('🔄 إنهاء تتبع الوقت للسجل $entryId');

      // إنهاء تتبع الوقت من API
      final entry = await _timeTrackingApiService.stopTimeTracking(entryId);

      if (entry != null) {
        // تحديث السجل في القائمة المحلية
        final index = _timeEntries.indexWhere((e) => e.id == entryId);
        if (index != -1) {
          _timeEntries[index] = entry;
        }

        _isTrackingTime.value = false;
        debugPrint('✅ تم إنهاء تتبع الوقت بنجاح');

        // إعادة تحميل بيانات تتبع الوقت
        if (currentTask != null) {
          await loadTaskTimeTracking(currentTask!.id);
          await loadTaskTimeTrackingSummary(currentTask!.id);

          // تحديث الواجهة
          updateUI(source: 'إنهاء تتبع الوقت');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنهاء تتبع الوقت: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات تتبع الوقت النشطة
  Future<List<TimeEntry>> getActiveTimeTrackingEntries() async {
    try {
      debugPrint('🔄 الحصول على سجلات تتبع الوقت النشطة');

      // الحصول على المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        debugPrint('⚠️ لا يوجد مستخدم مسجل دخول');
        return [];
      }

      // الحصول على السجلات النشطة من API
      final activeEntries = await _timeTrackingApiService.getActiveTimeEntry(currentUser.id);

      if (activeEntries != null) {
        debugPrint('✅ تم العثور على ${1} سجل نشط');
        return [activeEntries];
      } else {
        debugPrint('ℹ️ لا توجد سجلات نشطة');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على سجلات تتبع الوقت النشطة: $e');
      return [];
    }
  }



  /// تصدير تقرير أداء المستخدم
  Future<String?> exportUserPerformanceReport() async {
    try {
      // TODO: تنفيذ تصدير تقرير أداء المستخدم
      debugPrint('تصدير تقرير أداء المستخدم');
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير أداء المستخدم: $e');
      return null;
    }
  }

  /// تحميل ملخص تقدم المهمة
  Future<void> loadTaskProgressSummary(int taskId) async {
    try {
      debugPrint('🔄 تحميل ملخص تقدم المهمة $taskId...');

      // تحميل بيانات التقدم من API
      final trackers = await _progressTrackersApiService.getTrackersByTask(taskId);

      if (trackers.isEmpty) {
        debugPrint('ℹ️ لا توجد بيانات تقدم للمهمة $taskId');
        _progressSummary.value = null;
        return;
      }

      // حساب آخر تحديث
      final lastUpdated = trackers.map((t) => DateTime.fromMillisecondsSinceEpoch(t.updatedAt * 1000))
          .reduce((a, b) => a.isAfter(b) ? a : b);

      // حساب مساهمات المستخدمين
      final Map<String, double> userContributions = {};
      final Map<String, List<TaskProgressTracker>> userTrackers = {};

      // تجميع المتتبعات حسب المستخدم
      for (final tracker in trackers) {
        final userId = tracker.userId.toString();
        userTrackers.putIfAbsent(userId, () => []);
        userTrackers[userId]!.add(tracker);
      }

      // حساب مساهمة كل مستخدم
      for (final entry in userTrackers.entries) {
        final userId = entry.key;
        final userTrackersList = entry.value;

        // حساب إجمالي نسبة المساهمة للمستخدم
        final totalContribution = userTrackersList.fold<double>(
          0.0, (sum, tracker) => sum + tracker.contributionPercentage
        );

        userContributions[userId] = totalContribution;
      }

      // حساب التقدم الحالي
      final currentProgress = (_currentTask.value?.completionPercentage ?? 0.0).toDouble();

      // حساب إحصائيات إضافية
      final totalUpdates = trackers.length;
      final averageProgressPerUpdate = totalUpdates > 0
          ? trackers.fold<double>(0.0, (sum, t) => sum + t.progressPercentage) / totalUpdates
          : 0.0;

      // الحصول على آخر 5 تحديثات
      final recentUpdates = List<TaskProgressTracker>.from(trackers)
        ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt))
        ..take(5);

      // إنشاء ملخص التقدم
      final summary = TaskProgressSummary(
        taskId: taskId,
        currentProgress: currentProgress,
        lastUpdated: lastUpdated,
        userContributions: userContributions,
        totalUpdates: totalUpdates,
        averageProgressPerUpdate: averageProgressPerUpdate,
        recentUpdates: recentUpdates.toList(),
        dailyProgressRate: _calculateDailyProgressRate(trackers),
        estimatedDaysToCompletion: _calculateEstimatedDaysToCompletion(trackers, currentProgress),
        totalPercentage: currentProgress,
      );

      _progressSummary.value = summary;
      debugPrint('✅ تم إنشاء ملخص التقدم: ${userContributions.length} مساهم، آخر تحديث: $lastUpdated');

    } catch (e) {
      debugPrint('❌ خطأ في تحميل ملخص تقدم المهمة: $e');
      _progressSummary.value = null;
    }
  }

  /// حساب معدل التقدم اليومي
  double? _calculateDailyProgressRate(List<TaskProgressTracker> trackers) {
    if (trackers.length < 2) return null;

    // ترتيب المتتبعات حسب التاريخ
    final sortedTrackers = List<TaskProgressTracker>.from(trackers)
      ..sort((a, b) => a.updatedAt.compareTo(b.updatedAt));

    final firstTracker = sortedTrackers.first;
    final lastTracker = sortedTrackers.last;

    final firstDate = DateTime.fromMillisecondsSinceEpoch(firstTracker.updatedAt * 1000);
    final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTracker.updatedAt * 1000);

    final daysDifference = lastDate.difference(firstDate).inDays;
    if (daysDifference <= 0) return null;

    // حساب إجمالي التقدم
    final totalProgress = sortedTrackers.fold<double>(
      0.0, (sum, tracker) => sum + tracker.contributionPercentage
    );

    return totalProgress / daysDifference;
  }

  /// حساب الأيام المتوقعة للإكمال
  double? _calculateEstimatedDaysToCompletion(List<TaskProgressTracker> trackers, double currentProgress) {
    final dailyRate = _calculateDailyProgressRate(trackers);
    if (dailyRate == null || dailyRate <= 0) return null;

    final remainingProgress = 100.0 - currentProgress;
    if (remainingProgress <= 0) return 0.0;

    return remainingProgress / dailyRate;
  }

  /// تحميل متتبعات تقدم المهمة
  Future<void> loadTaskProgressTrackers(int taskId) async {
    try {
      debugPrint('🔄 تحميل متتبعات تقدم المهمة $taskId...');

      // تحميل متتبعات التقدم من API
      final trackers = await _progressTrackersApiService.getTrackersByTask(taskId);

      // تحويل إلى Map للتوافق مع الكود الحالي
      final trackersMap = trackers.map((tracker) => tracker.toJson()).toList();

      _progressTrackers.clear();
      _progressTrackers.addAll(trackersMap);

      // تحديث الواجهة
      update(['task_details', 'task_progress']);

      debugPrint('✅ تم تحميل ${_progressTrackers.length} متتبع تقدم للمهمة $taskId');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل متتبعات تقدم المهمة: $e');
      _progressTrackers.clear();
    }
  }

  /// تحميل ملخص تتبع الوقت للمهمة
  Future<void> loadTaskTimeTrackingSummary(int taskId) async {
    try {
      debugPrint('🔄 تحميل ملخص تتبع الوقت للمهمة $taskId...');

      // إنشاء ملخص من بيانات المساهمين الفعلية
      final Map<String, int> userMinutes = {};
      final Map<String, List<dynamic>> userEntries = {};

      // استخدام بيانات المساهمين لإنشاء ملخص الوقت
      for (final contributor in _taskContributors) {
        final userId = contributor.userId.toString();

        // حساب الوقت المقدر بناءً على المساهمة (تقدير: 1% = 30 دقيقة)
        final estimatedMinutes = (contributor.contributionPercentage * 30).round();
        userMinutes[userId] = estimatedMinutes;
        userEntries[userId] = [];
      }

      final totalMinutes = userMinutes.values.fold<int>(0, (sum, minutes) => sum + minutes);
      final activeEntries = _taskContributors.where((c) => c.isActiveContributor).length;

      // استخدام آخر تحديث من ملخص التقدم إذا كان متوفراً
      final lastActivity = _progressSummary.value?.lastUpdated ?? DateTime.now();

      final summary = TaskTimeTrackingSummary(
        taskId: taskId,
        userMinutes: userMinutes,
        totalMinutes: totalMinutes,
        activeEntries: activeEntries,
        lastActivity: lastActivity,
        lastUpdated: lastActivity,
        userEntries: userEntries,
      );

      _timeTrackingSummary.value = summary;
      debugPrint('✅ تم إنشاء ملخص تتبع الوقت: ${userMinutes.length} مستخدم، إجمالي: $totalMinutes دقيقة');

    } catch (e) {
      debugPrint('❌ خطأ في تحميل ملخص تتبع الوقت: $e');
      _timeTrackingSummary.value = null;
    }
  }

  /// إضافة مرفق للمهمة مع تحديث سريع للمرفقات
  Future<Attachment?> addAttachment(int taskId, int userId, File file,
      {String? description, String? metadata, Function(double)? onUploadProgress}) async {
    _isLoading.value = true;
    _error.value = '';
    try {
      debugPrint('📤 بدء رفع المرفق للمهمة $taskId...');

      // رفع الملف للخادم
      final newAttachment = await _attachmentsApiService.uploadAttachmentFile(
        file,
        taskId: taskId,
        userId: userId,
        description: description,
        metadata: metadata,
        onUploadProgress: onUploadProgress,
      );
      
      if (newAttachment != null) {
        debugPrint('✅ تم رفع المرفق بنجاح: ${newAttachment.fileName}');

        // تحديث سريع للمرفقات من الخادم لضمان التزامن
        await loadTaskAttachments(taskId);

        // تحديث فوري وشامل للواجهة مع تأخير قصير لضمان التحديث
        update(['task_details', 'task_attachments', 'task_overview']);
        refresh(); // إشعار جميع المستمعين

        // تحديث إضافي بعد تأخير قصير لضمان تحديث الواجهة
        await Future.delayed(const Duration(milliseconds: 100));
        update(['task_details', 'task_attachments', 'task_overview']);

        // تحديث تلقائي للتقدم عند رفع مرفق
        debugPrint('🔄 بدء التحديث التلقائي للتقدم عند رفع المرفق...');

        final progressUpdated = await autoUpdateTaskProgress(
          taskId,
          userId,
          'attachment',
          actionDescription: 'تم رفع مرفق: ${newAttachment.fileName}',
        );

        if (progressUpdated) {
          debugPrint('✅ تم تحديث التقدم بنجاح عند رفع المرفق');

          // تحديث إضافي للواجهة بعد تحديث التقدم
          update(['task_details', 'task_attachments', 'task_overview', 'task_progress']);
          refresh();
        } else {
          debugPrint('⚠️ لم يتم تحديث التقدم عند رفع المرفق');
        }

        debugPrint('✅ تم تحديث قائمة المرفقات بعد الرفع');
        return newAttachment;
      } else {
        _error.value = 'فشل في إضافة المرفق من الخادم';
        return null;
      }
    } catch (e) {
      _error.value = 'خطأ في إضافة المرفق: $e';
      debugPrint('❌ خطأ في رفع المرفق للمهمة $taskId: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }
  /// تحديث سريع للمرفقات بعد عمليات الرفع أو الحذف
  /// بديل أسرع من refreshTaskDetails للمرفقات فقط
  Future<void> refreshTaskAttachments(int taskId) async {
    debugPrint('🔄 تحديث سريع لمرفقات المهمة $taskId...');
    try {
      // تحميل المرفقات الجديدة من السيرفر
      await loadTaskAttachments(taskId);
      // تحديث فوري للواجهة
      update(['task_details', 'task_attachments', 'task_overview']);
      // إشعار جميع المستمعين بالتحديث
      refresh();

      debugPrint('✅ تم التحديث السريع للمرفقات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المرفقات: $e');
    }
  }

  /// الحصول على متتبعات التقدم لمستخدم محدد في مهمة محددة
  Future<List<TaskProgressTracker>> getProgressTrackersForUserInTask(
    String taskId,
    String userId,
  ) async {
    _isLoading.value = true;
    try {
      // التحقق من صحة المعرفات
      final taskIdInt = int.tryParse(taskId);
      final userIdInt = int.tryParse(userId);

      if (taskIdInt == null || userIdInt == null) {
        debugPrint('❌ معرفات غير صالحة - taskId: $taskId, userId: $userId');
        return [];
      }

      // محاولة الحصول على المتتبعات من التخزين المؤقت أولاً
      final cacheKey = 'user_trackers_${userIdInt}_task_$taskIdInt';
      final cachedTrackers = _cacheService.get<List<TaskProgressTracker>>(
        cacheKey,
        fromJson: (data) => (data as List)
            .map((item) => TaskProgressTracker.fromJson(item))
            .toList(),
      );

      if (cachedTrackers != null && cachedTrackers.isNotEmpty) {
        debugPrint(
            '📦 تم تحميل ${cachedTrackers.length} متتبع من التخزين المؤقت');
        return cachedTrackers;
      }

      // الحصول على جميع متتبعات التقدم للمهمة
      final allTrackers =
          await _progressTrackersApiService.getTrackersByTask(taskIdInt);
      if (allTrackers.isEmpty) {
        debugPrint('ℹ️ لا توجد متتبعات للمهمة $taskIdInt');
        return [];
      }

      // تصفية المتتبعات للمستخدم المحدد
      final userTrackers = allTrackers
          .where((tracker) => tracker.updatedBy == userIdInt)
          .toList();

      if (userTrackers.isEmpty) {
        debugPrint(
            'ℹ️ لا توجد مساهمات للمستخدم $userIdInt في المهمة $taskIdInt');
        return [];
      }

      // تحسين المتتبعات بإضافة معلومات إضافية
      final enhancedTrackers = userTrackers.map((tracker) {
        return tracker.copyWith(
          evidenceType: _determineEvidenceType(tracker),
          evidenceDescription: _generateEvidenceDescription(tracker),
          contributionPercentage: _calculateContribution(tracker, userTrackers),
        );
      }).toList();

      // ترتيب المتتبعات حسب التاريخ (الأحدث أولاً)
      enhancedTrackers.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      // حفظ في التخزين المؤقت
      await _cacheService.set(
        cacheKey,
        enhancedTrackers.map((tracker) => tracker.toJson()).toList(),
        ttl: const Duration(minutes: 5),
      );

      debugPrint(
          '✅ تم تحميل ${enhancedTrackers.length} متتبع للمستخدم $userIdInt في المهمة $taskIdInt');
      return enhancedTrackers;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على متتبعات التقدم للمستخدم: $e');
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  /// حساب نسبة المساهمة بناءً على سياق المتتبعات
  double _calculateContribution(
      TaskProgressTracker tracker, List<TaskProgressTracker> allUserTrackers) {
    // إذا كان هناك متتبع واحد فقط، فإن المساهمة هي 100%
    if (allUserTrackers.length == 1) {
      return 100.0;
    }

    // إذا كان المتتبع يحتوي على نسبة تقدم، استخدمها كأساس
    if (tracker.progressPercentage > 0) {
      // حساب المساهمة النسبية بناءً على مجموع نسب التقدم
      final totalProgress = allUserTrackers.fold<double>(
          0.0, (sum, t) => sum + (t.progressPercentage));
      if (totalProgress > 0) {
        return (tracker.progressPercentage / totalProgress) * 100;
      }
    }

    // توزيع المساهمة بالتساوي بين جميع المتتبعات
    return 100.0 / allUserTrackers.length;
  }

  /// تحديد نوع الدليل بناءً على بيانات المتتبع
  String _determineEvidenceType(TaskProgressTracker tracker) {
    // منطق محسن لتحديد نوع الدليل
    if (tracker.notes == null || tracker.notes!.isEmpty) {
      // إذا لم تكن هناك ملاحظات، نعتمد على نسبة التقدم
      if (tracker.progressPercentage >= 100) {
        return 'completion';
      } else if (tracker.progressPercentage > 0) {
        return 'progress';
      } else {
        return 'activity';
      }
    }

    // تحليل الملاحظات لتحديد النوع
    final notes = tracker.notes!.toLowerCase();

    if (notes.contains('تعليق') || notes.contains('comment')) {
      return 'comment';
    } else if (notes.contains('ملف') ||
        notes.contains('مرفق') ||
        notes.contains('file') ||
        notes.contains('attachment')) {
      return 'file';
    } else if (notes.contains('تحويل') || notes.contains('transfer')) {
      return 'transfer';
    } else if (notes.contains('إكمال') ||
        notes.contains('إنجاز') ||
        notes.contains('complete')) {
      return 'completion';
    } else if (notes.contains('تقدم') || notes.contains('progress')) {
      return 'progress';
    } else {
      return 'activity';
    }
  }

  /// إنشاء وصف للدليل بشكل أكثر تفصيلاً
  String _generateEvidenceDescription(TaskProgressTracker tracker) {
    final type = _determineEvidenceType(tracker);
    final progressText = tracker.progressPercentage > 0
        ? ' بنسبة ${tracker.progressPercentage}%'
        : '';
    final dateText = ' في ${_formatDate(tracker.updatedAt)}';

    switch (type) {
      case 'comment':
        return 'تم حساب المساهمة بناءً على إضافة تعليق$dateText';
      case 'file':
        return 'تم حساب المساهمة بناءً على إرفاق ملف$dateText';
      case 'transfer':
        return 'تم حساب المساهمة بناءً على تحويل المهمة$dateText';
      case 'completion':
        return 'تم حساب المساهمة بناءً على إكمال المهمة$dateText';
      case 'progress':
        return 'تم حساب المساهمة بناءً على تحديث التقدم$progressText$dateText';
      default:
        return 'تم حساب المساهمة بناءً على النشاط في المهمة$dateText';
    }
  }

  /// تنسيق التاريخ بشكل مقروء
  String _formatDate(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  // خصائص إضافية مطلوبة - إضافة getters للمتغيرات الجديدة

  /// إضافة تعليق (دالة مساعدة للتوافق)
  Future<bool> addComment(int taskId, String content) async {
    // final taskIdInt = int.tryParse(taskId); // No longer needed
    // if (taskIdInt == null) return false; // No longer needed
    return await addTaskComment(taskId, content);
  }

  /// إضافة رسالة للمهمة (دالة مساعدة للتوافق)
  void addMessageToTask(Map<String, dynamic> message) {
    if (message.containsKey('taskId') && message.containsKey('content')) {
      _taskMessages.add(message);
      debugPrint('تمت إضافة رسالة جديدة للمهمة ${message['taskId']}');
    } else {
      debugPrint('خطأ: بيانات الرسالة غير مكتملة');
    }
  }

  /// إرسال رسالة مهمة (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> sendTaskMessage(
    String taskId,
    String content, {
    int? contentType,
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    try {
      _isSendingMessage.value = true;

      // محاكاة إرسال رسالة
      await Future.delayed(const Duration(seconds: 1));

      final message = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'taskId': taskId,
        'senderId': UserHelper.getCurrentUserId().toString(),
        'content': content,
        'contentType': contentType ?? 1,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'mentionedUserIds': mentionedUserIds,
        'replyToMessageId': replyToMessageId,
      };

      _taskMessages.add(message);
      return message;
    } catch (e) {
      debugPrint('خطأ في إرسال رسالة المهمة: $e');
      return null;
    } finally {
      _isSendingMessage.value = false;
    }
  }

  /// الحصول على رسالة بالمعرف (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> getMessageById(String messageId) async {
    try {
      return _taskMessages.firstWhere(
        (message) => message['id'] == messageId,
        orElse: () => {},
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسالة: $e');
      return null;
    }
  }

  /// إضافة مرفق لرسالة (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> addMessageAttachment(
    String messageId,
    dynamic file,
    String fileName,
  ) async {
    try {
      final attachment = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'messageId': messageId,
        'fileName': fileName,
        'fileSize': 0,
        'fileType': fileName.split('.').last,
        'uploadedAt': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      };

      _messageAttachments.add(attachment);
      return attachment;
    } catch (e) {
      debugPrint('خطأ في إضافة مرفق الرسالة: $e');
      return null;
    }
  }

  /// تشخيص مشاكل API للمهام
  Future<void> diagnoseTasks() async {
    try {
      debugPrint('🔍 بدء تشخيص مشاكل API للمهام...');

      // تشخيص سريع للمهام
      final quickResults = await ApiDiagnostics.quickTasksTest();

      debugPrint('\n📊 نتائج التشخيص السريع:');
      for (final entry in quickResults.entries) {
        final testName = entry.key;
        final result = entry.value as Map<String, dynamic>;
        final status = result['success'] ? '✅' : '❌';
        final statusCode = result['statusCode'] ?? 'N/A';
        final responseTime = result['responseTime'] ?? 0;

        debugPrint('$status $testName: $statusCode (${responseTime}ms)');

        if (!result['success'] && result.containsKey('error')) {
          debugPrint('   ❌ الخطأ: ${result['error']}');
        }

        if (result.containsKey('dataCount')) {
          debugPrint('   📊 عدد العناصر: ${result['dataCount']}');
        }
      }

      // إذا فشل الاختبار الأساسي، نقوم بتشخيص شامل
      final basicTest = quickResults['basic'] as Map<String, dynamic>;
      if (!basicTest['success']) {
        debugPrint('\n🔍 الاختبار الأساسي فشل، بدء التشخيص الشامل...');
        final fullResults = await ApiDiagnostics.runFullDiagnostics();
        ApiDiagnostics.printDiagnosticsReport(fullResults);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشخيص API: $e');
    }
  }

  /// محاولة إصلاح مشاكل المهام
  Future<void> attemptTasksFix() async {
    try {
      debugPrint('🔧 محاولة إصلاح مشاكل المهام...');

      // مسح التخزين المؤقت
      await _cacheService.remove(CacheKeys.tasks);
      debugPrint('✅ تم مسح التخزين المؤقت للمهام');

      // إعادة تهيئة الخدمات
      await _initializeServices();
      debugPrint('✅ تم إعادة تهيئة الخدمات');

      // محاولة تحميل المهام مع إعادة المحاولة
      for (int attempt = 1; attempt <= 3; attempt++) {
        debugPrint('🔄 محاولة تحميل المهام - المحاولة $attempt/3');

        try {
          // استخدام الدالة الموحدة بدلاً من loadAllTasks
          final authController = Get.find<AuthController>();
          if (authController.currentUser.value != null) {
            await loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);
          }
          if (_allTasks.isNotEmpty) {
            debugPrint('✅ تم إصلاح مشكلة المهام بنجاح');
            Get.snackbar(
              'تم الإصلاح',
              'تم حل مشكلة تحميل المهام بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green.shade100,
              colorText: Colors.green.shade800,
              duration: const Duration(seconds: 3),
              icon: const Icon(Icons.check_circle, color: Colors.green),
            );
            return;
          }
        } catch (e) {
          debugPrint('❌ فشلت المحاولة $attempt: $e');
          if (attempt < 3) {
            await Future.delayed(Duration(seconds: attempt * 2));
          }
        }
      }

      debugPrint('❌ فشل في إصلاح مشكلة المهام');
      Get.snackbar(
        'فشل الإصلاح',
        'لم يتم حل مشكلة تحميل المهام. يرجى التحقق من الاتصال بالخادم.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 5),
        icon: const Icon(Icons.error, color: Colors.red),
      );
    } catch (e) {
      debugPrint('❌ خطأ في محاولة الإصلاح: $e');
    }
  }

  /// تحميل المرفقات للمهام التي لا تحتوي على مرفقات
  Future<void> _loadAttachmentsForTasks(List<Task> tasks) async {
    try {
      debugPrint('🔗 بدء تحميل المرفقات للمهام...');

      for (int i = 0; i < tasks.length; i++) {
        final task = tasks[i];

        // إذا كانت المهمة لا تحتوي على مرفقات، نحاول تحميلها
        if (task.attachments.isEmpty) {
          try {
            final attachments = await _attachmentsApiService.getAttachmentsByTask(task.id);
            if (attachments.isNotEmpty) {
              debugPrint('🔗 تم العثور على ${attachments.length} مرفق للمهمة ${task.id}');

              // تحديث المهمة بالمرفقات الجديدة
              final updatedTask = task.copyWith(attachments: attachments);
              tasks[i] = updatedTask;

              // تحديث القائمة الرئيسية أيضاً
              final taskIndex = _allTasks.indexWhere((t) => t.id == task.id);
              if (taskIndex != -1) {
                _allTasks[taskIndex] = updatedTask;
              }
            }
          } catch (e) {
            debugPrint('⚠️ خطأ في تحميل مرفقات المهمة ${task.id}: $e');
          }
        } else {
          debugPrint('🔗 المهمة ${task.id} تحتوي بالفعل على ${task.attachments.length} مرفق');
        }
      }

      debugPrint('✅ انتهى تحميل المرفقات للمهام');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المرفقات: $e');
    }
  }

  // إدارة اتصالات SignalR للمهام
  void joinTaskGroup(String taskId) {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      // استخدام joinChatGroup بدلاً من joinGroup
      signalR.joinChatGroup("Task_$taskId");

      // استخدام hubConnection?.on بدلاً من on مباشرة
      signalR.chatHubConnection?.on('ReceiveMessage', (message) {
        if (message != null && message.isNotEmpty) {
          taskMessages.add(message[0]);
          update(['messages']);
        }
      });
    } catch (e) {
      debugPrint('خطأ في الانضمام لمجموعة المهمة: $e');
    }
  }

  void leaveTaskGroup(String taskId) {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      // استخدام leaveChatGroup بدلاً من leaveGroup
      signalR.leaveChatGroup("Task_$taskId");

      // إزالة الاستماع للأحداث - لا يوجد دالة off مباشرة
      // يمكن إعادة الاتصال أو تجاهل الرسائل
    } catch (e) {
      debugPrint('خطأ في مغادرة مجموعة المهمة: $e');
    }
  }

  // إرسال رسالة مهمة عبر SignalR - تحديث للدالة الموجودة
  Future<void> addTaskMessage(Map<String, dynamic> message) async {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      final groupId = "Task_${message['taskId']}";

      // تحويل Map إلى كائن Message متوافق مع الهيكل المطلوب
      // نحتاج إلى تحويل البيانات لتتوافق مع توقعات Message.fromJson
      final messageData = {
        'id': int.tryParse(message['id']?.toString() ?? '') ??
            DateTime.now().millisecondsSinceEpoch,
        'groupId': int.tryParse(message['taskId']?.toString() ?? '') ?? 0,
        'senderId': int.tryParse(message['senderId']?.toString() ?? '') ?? 0,
        'content': message['content'] ?? '',
        // التأكد من أن contentType هو int
        'contentType': (message['contentType'] is int
                ? message['contentType']
                : (message['contentType'] as MessageContentType?)?.value) ??
            1,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': null,
        'isDeleted': false,
        'isRead': false,
        'isPinned': false,
        'pinnedAt': null,
        'pinnedBy': null,
        // التأكد من أن priority هو int
        'priority': (message['priority'] is int
                ? message['priority']
                : (message['priority'] as MessagePriority?)?.value) ??
            0,
        'isMarkedForFollowUp': false,
        'followUpAt': null,
        'markedForFollowUpBy': null,
        'isEdited': false,
        'receiverId': null,
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'mentionedUserIds': message['mentionedUserIds'] ?? [],
        'senderName': message['senderName'] ?? ''
      };

      // محاولة إرسال الرسالة عبر SignalR إذا كان متاحًا
      await signalR.sendMessageToHub(groupId, Message.fromJson(messageData));

      // إضافة الرسالة محليًا
      taskMessages.add(message);
      update(['messages']);
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      Get.snackbar('خطأ', 'فشل في إرسال الرسالة: $e');

      // في حالة فشل إرسال الرسالة عبر SignalR، نضيفها محليًا فقط
      if (message.containsKey('taskId') && message.containsKey('content')) {
        taskMessages.add(message);
        update(['messages']);
        debugPrint('تمت إضافة رسالة محلية للمهمة ${message['taskId']}');
      }
    }
  }

  /// تحميل جميع المساهمين المحتملين للمهمة (المستخدمين الذين لديهم صلاحية وصول)
  Future<void> loadTaskContributors(int taskId) async {
    try {
      debugPrint('🔄 تحميل المساهمين للمهمة $taskId...');

      final contributors = await _taskAccessApiService.getTaskContributors(taskId);

      // تحديث البيانات بدون تغيير حالة التحميل لتجنب مشكلة Obx
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _taskContributors.assignAll(contributors);
        // تحديث قائمة المساهمات القديمة للتوافق مع الكود الموجود
        _updateUserContributionsFromContributors(contributors);

        // تحديث الواجهة
        update(['task_contributors', 'task_details', 'task_overview']);
      });

      debugPrint('✅ تم تحميل ${contributors.length} مساهم للمهمة $taskId');

      // طباعة تفاصيل المساهمين للتشخيص
      for (final contributor in contributors) {
        debugPrint('📊 مساهم: ${contributor.userName} - نشط: ${contributor.isActiveContributor} - نسبة: ${contributor.contributionPercentage}%');
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحميل المساهمين: $e');
      // تحديث الخطأ بعد انتهاء البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _error.value = 'فشل في تحميل المساهمين: $e';
      });
    }
  }

  /// تحديث قائمة المساهمات القديمة من قائمة المساهمين الجديدة للتوافق
  void _updateUserContributionsFromContributors(List<TaskContributor> contributors) {
    final contributionsMap = contributors.map((contributor) => {
      'userId': contributor.userId.toString(),
      'userName': contributor.userName,
      'percentage': contributor.contributionPercentage,
      'totalUpdates': contributor.totalUpdates,
      'totalComments': contributor.totalComments,
      'isActiveContributor': contributor.isActiveContributor,
    }).toList();

    _userContributions.assignAll(contributionsMap);
    debugPrint('🔄 تم تحديث قائمة المساهمات للتوافق مع الكود الموجود');
  }

  /// الحصول على المساهمين النشطين فقط
  List<TaskContributor> getActiveContributors() {
    return _taskContributors.where((contributor) => contributor.isActiveContributor).toList();
  }

  /// الحصول على المساهمين المحتملين (الذين لم يساهموا بعد)
  List<TaskContributor> getPotentialContributors() {
    return _taskContributors.where((contributor) => !contributor.isActiveContributor).toList();
  }

  /// الحصول على أفضل المساهمين (مرتبين حسب نسبة المساهمة)
  List<TaskContributor> getTopContributors({int limit = 5}) {
    final activeContributors = getActiveContributors();
    activeContributors.sort((a, b) => b.contributionPercentage.compareTo(a.contributionPercentage));
    return activeContributors.take(limit).toList();
  }

  /// البحث عن مساهم بواسطة معرف المستخدم
  // TaskContributor? getContributorByUserId(int userId) {
  //   try {
  //     return _taskContributors.firstWhere((contributor) => contributor.userId == userId);
  //   } catch (e) {
  //     return null;
  //   }
  // }

  /// التحقق من كون المستخدم مساهم في المهمة
  bool isUserContributor(int userId) {
    return _taskContributors.any((contributor) => contributor.userId == userId);
  }

  /// التحقق من كون المستخدم مساهم نشط في المهمة
  bool isUserActiveContributor(int userId) {
    final contributor = getContributorByUserId(userId);
    return contributor?.isActiveContributor ?? false;
  }

  /// الحصول على إحصائيات المساهمات
  Map<String, dynamic> getContributionStatistics() {
    final totalContributors = _taskContributors.length;
    final activeContributors = getActiveContributors().length;
    final potentialContributors = getPotentialContributors().length;

    final totalContributionPercentage = _taskContributors
        .fold<double>(0.0, (sum, contributor) => sum + contributor.contributionPercentage);

    final averageContribution = totalContributors > 0
        ? totalContributionPercentage / totalContributors
        : 0.0;

    return {
      'totalContributors': totalContributors,
      'activeContributors': activeContributors,
      'potentialContributors': potentialContributors,
      'totalContributionPercentage': totalContributionPercentage,
      'averageContribution': averageContribution,
      'contributionRate': totalContributors > 0
          ? (activeContributors / totalContributors * 100)
          : 0.0,
    };
  }
}
